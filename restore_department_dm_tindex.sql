-- Восстановление dm_tindex в таблице geonpi.public.department
-- Обновляем только те записи, где dm_tindex отличается от значения в CSV

UPDATE geonpi.public.department 
SET dm_tindex = CASE id
    WHEN 55124 THEN '------'
    WHEN 3353 THEN '------'
    WHEN 55241 THEN '------'
    WHEN 16322 THEN '------'
    WHEN 5906 THEN '------'
    WHEN 1153610 THEN '------'
    WHEN 1692132 THEN '------'
    WHEN 5624 THEN '------'
    WHEN 156 THEN '------'
    WHEN 1693057 THEN '------'
    WHEN 6237 THEN '------'
    WHEN 55290 THEN '------'
    WHEN 55319 THEN '------'
    WHEN 20277 THEN '2909'
    WHEN 55400 THEN '------'
    WHEN 55509 THEN '------'
    WHEN 55125 THEN '------'
    WHEN 3354 THEN '------'
    WHEN 54985 THEN '------'
    WHEN 55242 THEN '------'
    WHEN 55320 THEN '------'
    WHEN 55291 THEN '------'
    WHEN 55401 THEN '------'
    WHEN 2927 THEN '15517'
    WHEN 14054 THEN '15168'
    WHEN 1181 THEN '------'
    WHEN 55243 THEN '------'
    WHEN 55126 THEN '------'
    WHEN 13759 THEN '------'
    WHEN 34271 THEN '------'
    WHEN 54532 THEN '------'
    WHEN 55402 THEN '------'
    WHEN 2827 THEN '15459'
    WHEN 26931 THEN '------'
    WHEN 54263 THEN '------'
    WHEN 12036 THEN '------'
    WHEN 55127 THEN '------'
    WHEN 54272 THEN '------'
    WHEN 55244 THEN '------'
    WHEN 3130 THEN '3130'
    WHEN 55048 THEN '------'
    WHEN 55403 THEN '------'
    WHEN 18698 THEN '------'
    WHEN 55510 THEN '------'
    WHEN 26914 THEN '------'
    WHEN 2944 THEN '------'
    WHEN 3540 THEN '------'
    WHEN 55128 THEN '------'
    WHEN 53832 THEN '------'
    WHEN 54826 THEN '------'
    WHEN 53179 THEN '------'
    WHEN 34431 THEN '------'
    WHEN 55245 THEN '------'
    WHEN 55404 THEN '------'
    WHEN 3053 THEN '------'
    WHEN 2672 THEN '2674'
    WHEN 41892 THEN '------'
    WHEN 55129 THEN '------'
    WHEN 31871 THEN '------'
    WHEN 55049 THEN '------'
    WHEN 3775 THEN '------'
    WHEN 55246 THEN '------'
    WHEN 42714 THEN '------'
    WHEN 15140 THEN '------'
    WHEN 55405 THEN '------'
    WHEN 55321 THEN '------'
    WHEN 3853 THEN '15405'
    WHEN 55085 THEN '------'
    WHEN 32841 THEN '------'
    WHEN 54321 THEN '54313'
    WHEN 54362 THEN '------'
    WHEN 54363 THEN '------'
    WHEN 54364 THEN '------'
    WHEN 53839 THEN '------'
    WHEN 54365 THEN '------'
    WHEN 3679 THEN '------'
    WHEN 55130 THEN '------'
    WHEN 55247 THEN '------'
    WHEN 1257 THEN '------'
    WHEN 54987 THEN '------'
    WHEN 55406 THEN '------'
    WHEN 26635 THEN '------'
    WHEN 26919 THEN '------'
    WHEN 55322 THEN '------'
    WHEN 27216 THEN '------'
    WHEN 27392 THEN '------'
    WHEN 55131 THEN '------'
    WHEN 1133 THEN '------'
    WHEN 55248 THEN '------'
    WHEN 54781 THEN '------'
    WHEN 54381 THEN '------'
    WHEN 53815 THEN '------'
    WHEN 26651 THEN '------'
    WHEN 55323 THEN '------'
    WHEN 54366 THEN '------'
    WHEN 54367 THEN '------'
    WHEN 54378 THEN '------'
    WHEN 55407 THEN '------'
    WHEN 55511 THEN '------'
    WHEN 22546 THEN '------'
    WHEN 23282 THEN '------'
    WHEN 55086 THEN '------'
    WHEN 22544 THEN '------'
    WHEN 54988 THEN '------'
    WHEN 2979 THEN '------'
    WHEN 55249 THEN '------'
    WHEN 15142 THEN '------'
    WHEN 887 THEN '------'
    WHEN 32371 THEN '------'
    WHEN 55132 THEN '------'
    WHEN 55408 THEN '------'
    WHEN 55512 THEN '------'
    WHEN 54037 THEN '------'
    WHEN 43335 THEN '------'
    WHEN 27657 THEN '------'
    WHEN 55255 THEN '------'
    WHEN 55133 THEN '------'
    WHEN 55050 THEN '------'
    WHEN 55409 THEN '------'
    WHEN 55250 THEN '------'
    WHEN 20137 THEN '------'
    WHEN 26926 THEN '------'
    WHEN 55513 THEN '------'
    WHEN 2912 THEN '2909'
    WHEN 26031 THEN '------'
    WHEN 4050 THEN '------'
    WHEN 55134 THEN '------'
    WHEN 55256 THEN '------'
    WHEN 55292 THEN '------'
    WHEN 55251 THEN '------'
    WHEN 2906 THEN '------'
    WHEN 55410 THEN '------'
    WHEN 53306 THEN '------'
    WHEN 26913 THEN '------'
    WHEN 26891 THEN '------'
    WHEN 2469 THEN '------'
    WHEN 55135 THEN '------'
    WHEN 55257 THEN '------'
    WHEN 55252 THEN '------'
    WHEN 55411 THEN '------'
    WHEN 55051 THEN '------'
    WHEN 1661 THEN '------'
    WHEN 2913 THEN '------'
    WHEN 55514 THEN '------'
    WHEN 55293 THEN '------'
    WHEN 39111 THEN '------'
    WHEN 2092 THEN '------'
    WHEN 18118 THEN '------'
    WHEN 2114 THEN '------'
    WHEN 3052 THEN '------'
    WHEN 55096 THEN '------'
    WHEN 55136 THEN '------'
    WHEN 55412 THEN '------'
    WHEN 55258 THEN '------'
    WHEN 6124 THEN '------'
    WHEN 55253 THEN '------'
    WHEN 55515 THEN '------'
    WHEN 55294 THEN '------'
    WHEN 54093 THEN '------'
    WHEN 52934 THEN '------'
END
WHERE id IN (55124,3353,55241,16322,5906,1153610,1692132,5624,156,1693057,6237,55290,55319,20277,55400,55509,55125,3354,54985,55242,55320,55291,55401,2927,14054,1181,55243,55126,13759,34271,54532,55402,2827,26931,54263,12036,55127,54272,55244,3130,55048,55403,18698,55510,26914,2944,3540,55128,53832,54826,53179,34431,55245,55404,3053,2672,41892,55129,31871,55049,3775,55246,42714,15140,55405,55321,3853,55085,32841,54321,54362,54363,54364,53839,54365,3679,55130,55247,1257,54987,55406,26635,26919,55322,27216,27392,55131,1133,55248,54781,54381,53815,26651,55323,54366,54367,54378,55407,55511,22546,23282,55086,22544,54988,2979,55249,15142,887,32371,55132,55408,55512,54037,43335,27657,55255,55133,55050,55409,55250,20137,26926,55513,2912,26031,4050,55134,55256,55292,55251,2906,55410,53306,26913,26891,2469,55135,55257,55252,55411,55051,1661,2913,55514,55293,39111,2092,18118,2114,3052,55096,55136,55412,55258,6124,55253,55515,55294,54093,52934);

-- Примечание: Этот скрипт содержит только первую часть данных (около 150 записей)
-- Для полного восстановления нужно создать дополнительные скрипты с остальными записями
